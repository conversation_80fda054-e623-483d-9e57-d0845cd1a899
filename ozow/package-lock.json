{"name": "ozow", "lockfileVersion": 3, "requires": true, "packages": {"": {"devDependencies": {"@iconify/json": "^2.2.314", "@iconify/tailwind4": "^1.0.6", "daisyui": "^5.0.0", "htmx.org": "2.0.4", "tailwindcss-animated": "^1.1.2"}}, "node_modules/@antfu/install-pkg": {"version": "1.0.0", "dev": true, "license": "MIT", "dependencies": {"package-manager-detector": "^0.2.8", "tinyexec": "^0.3.2"}, "funding": {"url": "https://github.com/sponsors/antfu"}}, "node_modules/@antfu/utils": {"version": "8.1.1", "dev": true, "license": "MIT", "funding": {"url": "https://github.com/sponsors/antfu"}}, "node_modules/@iconify/json": {"version": "2.2.316", "dev": true, "license": "MIT", "dependencies": {"@iconify/types": "*", "pathe": "^1.1.2"}}, "node_modules/@iconify/tailwind4": {"version": "1.0.6", "dev": true, "license": "MIT", "dependencies": {"@iconify/types": "^2.0.0", "@iconify/utils": "^2.2.1"}, "funding": {"url": "https://github.com/sponsors/cyberalien"}, "peerDependencies": {"tailwindcss": ">= 4"}}, "node_modules/@iconify/types": {"version": "2.0.0", "dev": true, "license": "MIT"}, "node_modules/@iconify/utils": {"version": "2.3.0", "dev": true, "license": "MIT", "dependencies": {"@antfu/install-pkg": "^1.0.0", "@antfu/utils": "^8.1.0", "@iconify/types": "^2.0.0", "debug": "^4.4.0", "globals": "^15.14.0", "kolorist": "^1.8.0", "local-pkg": "^1.0.0", "mlly": "^1.7.4"}}, "node_modules/acorn": {"version": "8.14.1", "dev": true, "license": "MIT", "bin": {"acorn": "bin/acorn"}, "engines": {"node": ">=0.4.0"}}, "node_modules/confbox": {"version": "0.2.1", "dev": true, "license": "MIT"}, "node_modules/daisyui": {"version": "5.0.3", "dev": true, "license": "MIT", "funding": {"url": "https://github.com/saadeghi/daisyui?sponsor=1"}}, "node_modules/debug": {"version": "4.4.0", "dev": true, "license": "MIT", "dependencies": {"ms": "^2.1.3"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/exsolve": {"version": "1.0.4", "dev": true, "license": "MIT"}, "node_modules/globals": {"version": "15.15.0", "dev": true, "license": "MIT", "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/htmx.org": {"version": "2.0.4", "dev": true, "license": "0BSD"}, "node_modules/kolorist": {"version": "1.8.0", "dev": true, "license": "MIT"}, "node_modules/local-pkg": {"version": "1.1.1", "dev": true, "license": "MIT", "dependencies": {"mlly": "^1.7.4", "pkg-types": "^2.0.1", "quansync": "^0.2.8"}, "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/antfu"}}, "node_modules/mlly": {"version": "1.7.4", "dev": true, "license": "MIT", "dependencies": {"acorn": "^8.14.0", "pathe": "^2.0.1", "pkg-types": "^1.3.0", "ufo": "^1.5.4"}}, "node_modules/mlly/node_modules/pathe": {"version": "2.0.3", "dev": true, "license": "MIT"}, "node_modules/mlly/node_modules/pkg-types": {"version": "1.3.1", "dev": true, "license": "MIT", "dependencies": {"confbox": "^0.1.8", "mlly": "^1.7.4", "pathe": "^2.0.1"}}, "node_modules/mlly/node_modules/pkg-types/node_modules/confbox": {"version": "0.1.8", "dev": true, "license": "MIT"}, "node_modules/ms": {"version": "2.1.3", "dev": true, "license": "MIT"}, "node_modules/package-manager-detector": {"version": "0.2.11", "dev": true, "license": "MIT", "dependencies": {"quansync": "^0.2.7"}}, "node_modules/pathe": {"version": "1.1.2", "dev": true, "license": "MIT"}, "node_modules/pkg-types": {"version": "2.1.0", "dev": true, "license": "MIT", "dependencies": {"confbox": "^0.2.1", "exsolve": "^1.0.1", "pathe": "^2.0.3"}}, "node_modules/pkg-types/node_modules/pathe": {"version": "2.0.3", "dev": true, "license": "MIT"}, "node_modules/quansync": {"version": "0.2.8", "dev": true, "funding": [{"type": "individual", "url": "https://github.com/sponsors/antfu"}, {"type": "individual", "url": "https://github.com/sponsors/sxzz"}], "license": "MIT"}, "node_modules/tailwindcss": {"version": "4.0.13", "dev": true, "license": "MIT", "peer": true}, "node_modules/tailwindcss-animated": {"version": "1.1.2", "dev": true, "license": "MIT", "peerDependencies": {"tailwindcss": ">=3.1.0"}}, "node_modules/tinyexec": {"version": "0.3.2", "dev": true, "license": "MIT"}, "node_modules/ufo": {"version": "1.5.4", "dev": true, "license": "MIT"}}}
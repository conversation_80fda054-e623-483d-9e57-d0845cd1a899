VER := $(shell cd ./services/ozow && git describe --always --long)-$(shell date -u +%Y-%m-%dT%H-%M-%S%Z)


start:
	air -c .air.toml

build:
	templ generate

css:
	bunx tailwindcss -i  ./services/ozow/input.css -o ./services/ozow/assets/css/style.css --minify

js:
	bunx terser ./services/ozow/node_modules/@sudodevnull/datastar/dist/datastar.js -o ./services/ozow/assets/js/datastar.js

web: build css js
	
image_build:
	podman build -t ozow:$(VER) -f ./services/ozow/ozow.Containerfile .

image_tag:
	podman tag localhost/ozow:$(VER) registry.cube.local:31027/ozow:$(VER)

image_push:
	podman push registry.cube.local:31027/ozow:$(VER) --tls-verify=false

ver:
	@echo $(VER)

image: image_build image_tag image_push ver


dev_image_build:
	podman build -t dev-ozow:$(VER) -f ./services/ozow/ozow-dev.Containerfile .

dev_image_tag:
	podman tag localhost/dev-ozow:$(VER) registry.cube.local:31027/dev-ozow:$(VER)

dev_image_push:
	podman push registry.cube.local:31027/dev-ozow:$(VER) --tls-verify=false

dev_image:  dev_image_build dev_image_tag dev_image_push ver
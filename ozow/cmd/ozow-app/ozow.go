package main

import (
	"aps/lib/amplifin/utils"
	"aps/services/ozow/assets"
	"aps/services/ozow/internal/handlers"
	"log"
	"net/http"
	"os"

	"github.com/joho/godotenv"
)

func init() {
	err := godotenv.Load(".env")
	if err != nil {
		log.Println("Prod environment")
	}
}

func main() {
	URL := os.Getenv("URL")
	mux := http.NewServeMux()

	//CSS and JS to be cached
	for path, handler := range map[string]http.Handler{
		utils.AddPre(utils.AddPre("/css/", URL), "GET "): http.StripPrefix(utils.AddPre("/", URL), http.FileServer(http.FS(assets.CSS))),
		utils.AddPre(utils.AddPre("/js/", URL), "GET "):  http.StripPrefix(utils.AddPre("/", URL), http.FileServer(http.FS(assets.JS))),
	} {
		mux.Handle(path, utils.CacheHandler(handler))
		log.Println("Registered handler for path", path)
	}
	//

	for path, handler := range map[string]http.HandlerFunc{
		utils.AddPre(utils.AddPre("/PaymentRequest", URL), "POST "): handlers.CreatePaymentRequest,
		utils.AddPre(utils.AddPre("/Refund", URL),
			"POST "): handlers.SubmitRefund,
		utils.AddPre(utils.AddPre("/Notify", URL), "POST "): handlers.Notify,

		//pages
		utils.AddPre(utils.AddPre("/pay", URL), "POST "):    handlers.WebPaymentRequest,
		utils.AddPre(utils.AddPre("/success", URL), "GET "): handlers.Success,
		utils.AddPre(utils.AddPre("/cancel", URL), "GET "):  handlers.Cancel,
		utils.AddPre(utils.AddPre("/error", URL), "GET "):   handlers.Error,
		utils.AddPre(utils.AddPre("/home", URL), "GET "):    handlers.Index,
		utils.AddPre(utils.AddPre("/reset", URL), "GET "):   handlers.Reset,
	} {
		mux.HandleFunc(path, handler)
		log.Println("Registered handler for path", path)
	}
	err := http.ListenAndServe(":3000", mux)
	log.Println("Starting server on port 3000")
	if err != nil {
		log.Fatal(err)
	}

}

package main

import (
	"aps/lib/amplifin/utils"
	"aps/services/ozow/assets"
	"aps/services/ozow/internal/handlers"
	"aps/services/ozow/internal/session"
	"fmt"
	"log"
	"net/http"
	"os"

	"github.com/go-redis/redis/v8"
	"github.com/joho/godotenv"
)

func init() {
	err := godotenv.Load(".env")
	if err != nil {
		log.Println("Prod environment")
	}
}

func main() {
	URL := os.Getenv("URL")

	// Initialize Redis connection
	redisAddr := os.Getenv("REDIS_ADDR")
	redisPassword := os.Getenv("REDIS_PASSWORD")
	redisDB := 0 // Default DB

	if redisAddr == "" {
		redisAddr = "localhost:6379" // Default Redis address
	}

	rdb := redis.NewClient(&redis.Options{
		Addr:     redisAddr,
		Password: redisPassword,
		DB:       redisDB,
	})

	// Test Redis connection
	ctx := rdb.Context()
	_, err := rdb.Ping(ctx).Result()
	if err != nil {
		log.Printf("Warning: Could not connect to Redis: %v", err)
		log.Println("Session management will not work properly without Redis")
	} else {
		fmt.Println("Successfully connected to Redis!")
	}

	// Initialize session store and auth handlers
	sessionStore := session.NewRedisStore(rdb)
	authHandlers := handlers.NewAuthHandlers(sessionStore)

	mux := http.NewServeMux()

	//CSS and JS to be cached
	for path, handler := range map[string]http.Handler{
		utils.AddPre(utils.AddPre("/css/", URL), "GET "): http.StripPrefix(utils.AddPre("/", URL), http.FileServer(http.FS(assets.CSS))),
		utils.AddPre(utils.AddPre("/js/", URL), "GET "):  http.StripPrefix(utils.AddPre("/", URL), http.FileServer(http.FS(assets.JS))),
	} {
		mux.Handle(path, utils.CacheHandler(handler))
		log.Println("Registered handler for path", path)
	}
	//

	// Authentication routes
	mux.HandleFunc(utils.AddPre(utils.AddPre("/login", URL), "GET "), authHandlers.LoginGetHandler)
	mux.HandleFunc(utils.AddPre(utils.AddPre("/login", URL), "POST "), authHandlers.LoginPostHandler)
	mux.HandleFunc(utils.AddPre(utils.AddPre("/logout", URL), "POST "), authHandlers.LogoutPostHandler)

	for path, handler := range map[string]http.HandlerFunc{
		utils.AddPre(utils.AddPre("/PaymentRequest", URL), "POST "): handlers.CreatePaymentRequest,
		utils.AddPre(utils.AddPre("/Refund", URL),
			"POST "): handlers.SubmitRefund,
		utils.AddPre(utils.AddPre("/Notify", URL), "POST "): handlers.Notify,

		//pages
		utils.AddPre(utils.AddPre("/pay", URL), "POST "):    handlers.WebPaymentRequest,
		utils.AddPre(utils.AddPre("/success", URL), "GET "): handlers.Success,
		utils.AddPre(utils.AddPre("/cancel", URL), "GET "):  handlers.Cancel,
		utils.AddPre(utils.AddPre("/error", URL), "GET "):   handlers.Error,
		utils.AddPre(utils.AddPre("/home", URL), "GET "):    authHandlers.HomeHandler, // Use auth handler for home
		utils.AddPre(utils.AddPre("/reset", URL), "GET "):   handlers.Reset,
	} {
		mux.HandleFunc(path, handler)
		log.Println("Registered handler for path", path)
	}
	err := http.ListenAndServe(":3000", mux)
	log.Println("Starting server on port 3000")
	if err != nil {
		log.Fatal(err)
	}

}

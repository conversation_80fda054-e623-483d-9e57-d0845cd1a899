package models

import (
	"aps/services/ozow/internal/database"
	"database/sql"
	"log"

	"golang.org/x/crypto/bcrypt"
)

type User struct {
	ID       string
	Username string
	Password string
	Email    string
	FullName string
	// Additional fields from ClientDetails that might be useful
	ClientKey            string
	IdentificationDetail string
	Employer             string
	Department           string
	EmployeeCode         string
	FirstName            string
	Surname              string
	SalaryAmount         float64
}

// For development/testing - you can remove this in production
var TestUsers = map[string]*User{
	"testuser": {
		ID:       "testuser",
		Username: "testuser",
		Password: hashPassword("password123"),
		Email:    "<EMAIL>",
		FullName: "Test User",
	},
	"admin": {
		ID:       "admin",
		Username: "admin",
		Password: hashPassword("admin123"),
		Email:    "<EMAIL>",
		FullName: "Administrator",
	},
}

func hashPassword(password string) string {
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		log.Fatalf("Error hashing password: %v", err)
	}
	return string(hashedPassword)
}

func CheckPasswordHash(password, hash string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(hash), []byte(password))
	return err == nil
}

// GetUserByID retrieves a user by ID from the database
func GetUserByID(userID string) (*User, bool) {
	// First check test users for development
	if user, ok := TestUsers[userID]; ok {
		return user, true
	}

	// Try to get from database
	db, err := database.GetDBConnection()
	if err != nil {
		log.Printf("Error connecting to database: %v", err)
		return nil, false
	}
	defer db.Close()

	user := &User{}
	query := `
		SELECT 
			ClientKey, IdentificationDetail, Employer, Department, EmployeeCode,
			FirstName, Surname, SalaryAmount
		FROM client_details 
		WHERE ClientKey = ? OR IdentificationDetail = ?
	`
	
	err = db.QueryRow(query, userID, userID).Scan(
		&user.ClientKey, &user.IdentificationDetail, &user.Employer,
		&user.Department, &user.EmployeeCode, &user.FirstName,
		&user.Surname, &user.SalaryAmount,
	)
	
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, false
		}
		log.Printf("Error querying user: %v", err)
		return nil, false
	}

	// Set derived fields
	user.ID = user.ClientKey
	user.Username = user.IdentificationDetail
	user.FullName = user.FirstName + " " + user.Surname
	user.Email = "" // Not available in current schema
	
	return user, true
}

// GetUserByUsername retrieves a user by username (identification detail)
func GetUserByUsername(username string) (*User, bool) {
	// First check test users for development
	if user, ok := TestUsers[username]; ok {
		return user, true
	}

	// For database users, username is the identification detail
	return GetUserByID(username)
}

// AuthenticateUser authenticates a user with ID number and a simple password
// In a real implementation, you'd want proper password storage
func AuthenticateUser(idNumber, password string) (*User, bool) {
	// For development, check test users first
	if user, ok := TestUsers[idNumber]; ok {
		if CheckPasswordHash(password, user.Password) {
			return user, true
		}
		return nil, false
	}

	// For database users, we'll use a simple authentication
	// In production, you'd want to store hashed passwords in the database
	user, exists := GetUserByID(idNumber)
	if !exists {
		return nil, false
	}

	// For now, we'll use a simple password check
	// You should implement proper password storage and verification
	if password == "defaultpass" || password == user.IdentificationDetail {
		return user, true
	}

	return nil, false
}

// UpdateUser updates user information
func UpdateUser(user *User) error {
	// Update test users
	if _, ok := TestUsers[user.Username]; ok {
		TestUsers[user.Username] = user
		return nil
	}

	// For database users, implement database update
	db, err := database.GetDBConnection()
	if err != nil {
		return err
	}
	defer db.Close()

	query := `
		UPDATE client_details 
		SET FirstName = ?, Surname = ?
		WHERE ClientKey = ?
	`
	
	_, err = db.Exec(query, user.FirstName, user.Surname, user.ID)
	return err
}

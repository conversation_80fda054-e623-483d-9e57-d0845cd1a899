package handlers

import (
	"context"
	"net/http"

	"aps/services/ozow/internal/session"
)

// contextKey is a custom type for context keys to avoid collisions.
type contextKey string

const userIDContextKey contextKey = "userID"

// AuthMiddleware checks if a user is logged in using the provided session store.
func AuthMiddleware(sessionStore session.Store, next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		userID, err := sessionStore.GetSession(r)
		if err != nil {
			// Log the error but don't expose sensitive details to the user
			http.Error(w, "Internal server error", http.StatusInternalServerError)
			return
		}

		if userID == "" {
			// No active session, redirect to login
			http.Redirect(w, r, "/ozow/login", http.StatusSeeOther)
			return
		}

		// Store user ID in request context for handlers to access
		ctx := r.Context()
		ctx = context.WithValue(ctx, userIDContextKey, userID)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// GetUserIDFromContext retrieves the user ID from the request context
func GetUserIDFromContext(r *http.Request) (string, bool) {
	userID, ok := r.Context().Value(userIDContextKey).(string)
	return userID, ok
}

// RequireAuth is a helper function to check if a user is authenticated
func RequireAuth(sessionStore session.Store, w http.ResponseWriter, r *http.Request) (string, bool) {
	userID, err := sessionStore.GetSession(r)
	if err != nil {
		http.Error(w, "Internal server error", http.StatusInternalServerError)
		return "", false
	}

	if userID == "" {
		http.Redirect(w, r, "/ozow/login", http.StatusSeeOther)
		return "", false
	}

	return userID, true
}

package handlers

import (
	"aps/services/ozow/internal/components"
	"aps/services/ozow/internal/views"
	"log"
	"net/http"
)

func Index(w http.ResponseWriter, r *http.Request) {
	err := views.Index().Render(r.Context(), w)
	if err != nil {
		log.Println(err)
	}
	return
}

func Success(w http.ResponseWriter, r *http.Request) {
	err := views.Success().Render(r.Context(), w)
	if err != nil {
		log.Println(err)
	}

}

func Cancel(w http.ResponseWriter, r *http.Request) {
	err := views.Cancel().Render(r.Context(), w)
	if err != nil {
		log.Println(err)
	}
	return
}

func Error(w http.ResponseWriter, r *http.Request) {
	err := views.Error().Render(r.Context(), w)
	if err != nil {
		log.Println(err)
	}
	return
}

func Reset(w http.ResponseWriter, r *http.Request) {
	err := components.Form().Render(r.Context(), w)
	if err != nil {
		log.Println(err)
	}
}

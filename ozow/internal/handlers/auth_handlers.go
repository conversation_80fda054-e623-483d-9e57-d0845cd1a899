package handlers

import (
	"log"
	"net/http"

	"aps/services/ozow/internal/models"
	"aps/services/ozow/internal/session"
	"aps/services/ozow/internal/views"
)

type AuthHandlers struct {
	SessionStore session.Store
}

func NewAuthHandlers(sessionStore session.Store) *AuthHandlers {
	return &AuthHandlers{SessionStore: sessionStore}
}

// LoginGetHandler displays the login page
func (h *AuthHandlers) LoginGetHandler(w http.ResponseWriter, r *http.Request) {
	// Check if user is already logged in
	userID, err := h.SessionStore.GetSession(r)
	if err != nil {
		log.Printf("Error getting session in LoginGetHandler: %v", err)
	}

	if userID != "" {
		// User is already logged in, redirect to home
		http.Redirect(w, r, "/ozow/home", http.StatusFound)
		return
	}

	err = views.Login(views.LoginData{Error: ""}).Render(r.Context(), w)
	if err != nil {
		log.Printf("Error rendering login page: %v", err)
		http.Error(w, "Internal server error", http.StatusInternalServerError)
	}
}

// LoginPostHandler handles login form submission
func (h *AuthHandlers) LoginPostHandler(w http.ResponseWriter, r *http.Request) {
	idNumber := r.FormValue("idNumber")
	password := r.FormValue("password")

	if idNumber == "" || password == "" {
		if r.Header.Get("HX-Request") == "true" {
			views.LoginForm(views.LoginData{Error: "Please enter both ID number and password"}).Render(r.Context(), w)
			return
		}
		views.Login(views.LoginData{Error: "Please enter both ID number and password"}).Render(r.Context(), w)
		return
	}

	user, ok := models.AuthenticateUser(idNumber, password)
	if !ok {
		if r.Header.Get("HX-Request") == "true" {
			views.LoginForm(views.LoginData{Error: "Invalid ID number or password"}).Render(r.Context(), w)
			return
		}
		views.Login(views.LoginData{Error: "Invalid ID number or password"}).Render(r.Context(), w)
		return
	}

	if err := h.SessionStore.SetSession(w, user.ID); err != nil {
		log.Printf("Error setting session: %v", err)
		http.Error(w, "Internal server error", http.StatusInternalServerError)
		return
	}

	if r.Header.Get("HX-Request") == "true" {
		w.Header().Set("HX-Redirect", "/ozow/home")
		w.WriteHeader(http.StatusOK)
		return
	}

	http.Redirect(w, r, "/ozow/home", http.StatusFound)
}

// LogoutPostHandler handles logout
func (h *AuthHandlers) LogoutPostHandler(w http.ResponseWriter, r *http.Request) {
	if err := h.SessionStore.DeleteSession(w, r); err != nil {
		log.Printf("Error deleting session on logout: %v", err)
		http.Error(w, "Internal server error", http.StatusInternalServerError)
		return
	}

	if r.Header.Get("HX-Request") == "true" {
		w.Header().Set("HX-Redirect", "/ozow/login")
		w.WriteHeader(http.StatusOK)
		return
	}

	http.Redirect(w, r, "/ozow/login", http.StatusFound)
}

// HomeHandler redirects to appropriate page based on authentication status
func (h *AuthHandlers) HomeHandler(w http.ResponseWriter, r *http.Request) {
	userID, err := h.SessionStore.GetSession(r)
	if err != nil {
		log.Printf("Error getting session in HomeHandler: %v", err)
		http.Error(w, "Internal server error", http.StatusInternalServerError)
		return
	}

	if userID != "" {
		// User is logged in, show the payment interface with user info
		user, ok := models.GetUserByID(userID)
		if !ok {
			// User not found, clear session and redirect to login
			h.SessionStore.DeleteSession(w, r)
			http.Redirect(w, r, "/ozow/login", http.StatusSeeOther)
			return
		}
		
		// Render the index page with user information
		err = views.IndexWithUser(user).Render(r.Context(), w)
		if err != nil {
			log.Printf("Error rendering index with user: %v", err)
			http.Error(w, "Internal server error", http.StatusInternalServerError)
		}
		return
	}
	
	// User not logged in, redirect to login
	http.Redirect(w, r, "/ozow/login", http.StatusFound)
}

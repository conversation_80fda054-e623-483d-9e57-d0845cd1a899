package database

import (
	"aps/lib/tools"
	"database/sql"
	"errors"
	"log"
	"os"
	"time"
)

func GetDBConnection() (*sql.DB, error) {
	dbHostname := os.Getenv("DBHOSTNAME")
	dbName := os.Getenv("DBNAME")
	dbPassword := os.Getenv("DBPASS")
	dbUser := os.Getenv("DBUSER")

	if dbHostname == "" || dbName == "" || dbPassword == "" || dbUser == "" {
		return nil, errors.New("missing database configuration")
	}

	dsn := tools.DSN(dbName, dbUser, dbPassword, dbHostname, "tcp")
	db, err := tools.GetDatabaseClient(dsn, "mysql")
	if err != nil {
		log.Printf("Error connecting to database: %v", err)
		return nil, err
	}

	db.SetMaxOpenConns(20)
	db.SetMaxIdleConns(20)
	db.SetConnMaxLifetime(time.Minute * 5)

	log.Println("Successfully connected to database")
	return db, nil
}

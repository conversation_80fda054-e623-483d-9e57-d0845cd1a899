package components

templ Redirect(url string) {
	<script data-url={ url }>
        ((url) => {
            setTimeout(() => {
                window.location.href = url;
            }, 5000);
        })(document.currentScript.getAttribute("data-url"))
    </script>
	<div class="grid-cols-2 text-center">
		<div role="alert" class="alert alert-success alert-outline ">
			<span>Redirecting</span>
			<span class="text-center icon-[line-md--compass-twotone-loop] text-3xl"></span>
		</div>
	</div>
	<span id="indicator" class="text-center icon-[line-md--loading-alt-loop] text-2xl"></span>
}

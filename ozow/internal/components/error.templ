package components

templ Error(message string) {
	<div class="inline-grid *:[grid-area:1/1]">
		<div class="status status-error animate-ping"></div>
		<div class="status status-error"></div>
	</div>
	<div class="badge badge-dash badge-error ">{ message }</div>
	<button
		class="content-center btn btn-outline btn-error btn-lg w-40"
		hx-get="/ozow/reset"
		hx-target="#toast"
		hx-indicator="#indicator"
		hx-swap="innerHTML"
	>
		Reset
		<span id="indicator" class="icon-[line-md--rotate-270] text-2xl"></span>
	</button>
}

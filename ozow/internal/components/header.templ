package components

import "aps/services/ozow/internal/models"

templ Header(Page string) {
	<div class="navbar bg-primary text-primary-content">
		<div class="navbar-start">
			<button class="text-xl btn btn-ghost">
				<a class="icon-[line-md--phone-call-loop] text-2xl"></a>
			</button>
		</div>
		<div class="hidden navbar-center lg:flex">
			<ul class="px-1 menu menu-horizontal">
				<li><a href="/ozow/home">{ Page }</a></li>
			</ul>
		</div>
		<div class="navbar-end">
			<label class="flex cursor-pointer gap-2">
				<span class="icon-[line-md--sun-rising-loop] text-2xl"></span>
				<input type="checkbox" value="night" class="toggle theme-controller"/>
				<span class="icon-[line-md--moon-rising-alt-loop] text-2xl"></span>
			</label>
		</div>
	</div>
}

templ HeaderWithUser(Page string, user *models.User) {
	<div class="navbar bg-primary text-primary-content">
		<div class="navbar-start">
			<button class="text-xl btn btn-ghost">
				<a class="icon-[line-md--phone-call-loop] text-2xl"></a>
			</button>
		</div>
		<div class="hidden navbar-center lg:flex">
			<ul class="px-1 menu menu-horizontal">
				<li><a href="/ozow/home">{ Page }</a></li>
			</ul>
		</div>
		<div class="navbar-end">
			<div class="flex items-center gap-4">
				<!-- User Info -->
				<div class="hidden sm:block">
					<span class="text-sm">Welcome, { user.FirstName }</span>
				</div>
				<!-- Logout Button -->
				<form hx-post="/ozow/logout" class="inline">
					<button type="submit" class="btn btn-ghost btn-sm">
						<span class="icon-[line-md--logout] text-xl"></span>
						<span class="hidden sm:inline">Logout</span>
					</button>
				</form>
				<!-- Theme Toggle -->
				<label class="flex cursor-pointer gap-2">
					<span class="icon-[line-md--sun-rising-loop] text-2xl"></span>
					<input type="checkbox" value="night" class="toggle theme-controller"/>
					<span class="icon-[line-md--moon-rising-alt-loop] text-2xl"></span>
				</label>
			</div>
		</div>
	</div>
}

package views

import "aps/services/ozow/internal/models"

templ Success() {
	<!DOCTYPE html>
	<html lang="en" data-theme="dark">
		<head>
			<meta charset="UTF-8"/>
			<meta name="viewport" content="width=device-width, initial-scale=1.0"/>
			<title>Success</title>
			<link
				rel="stylesheet"
				type="text/css"
				href={ models.STYLE }
			/>
			<link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>🍾</text></svg>"/>
		</head>
		<body>
			<script type="module" src={ models.JS }></script>
			<div class="flex min-h-screen items-center justify-center bg-opacity-80">
				<div class="card glass w-96 pt-6">
					<div class="animate-bounce text-center text-3xl animate-infinite animate-ease-linear">
						👍
					</div>
					<div class="card-body grid place-items-center">
						<h2 class="card-title pb-3">Payment Successful</h2>
						// <div class="card-actions justify-center">
						// 	<button onclick="window.location='https://example.com';" class="btn btn-primary">New Payment</button>
						// </div>
					</div>
				</div>
			</div>
		</body>
	</html>
}

templ Error() {
	<!DOCTYPE html>
	<html lang="en" data-theme="dark">
		<head>
			<meta charset="UTF-8"/>
			<meta name="viewport" content="width=device-width, initial-scale=1.0"/>
			<title>Cancelled</title>
			<link
				rel="stylesheet"
				type="text/css"
				href={ models.STYLE }
			/>
			<link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>❌</text></svg>"/>
		</head>
		<body>
			<script type="module" src={ models.JS }></script>
			<div class="flex min-h-screen items-center justify-center bg-gray-900 bg-opacity-80">
				<div class="card glass w-96 pt-6">
					<div class="animate-wiggle-more text-center text-3xl animate-infinite animate-ease-linear">
						😔
					</div>
					<div class="card-body grid place-items-center">
						<h2 class="card-title pb-3">Error Occurred</h2>
						// <div class="card-actions justify-center">
						// 	<button onclick="window.location='https://example.com';" class="btn btn-primary">New Payment</button>
						// </div>
					</div>
				</div>
			</div>
		</body>
	</html>
}

templ Cancel() {
	<!DOCTYPE html>
	<html lang="en" data-theme="dark">
		<head>
			<meta charset="UTF-8"/>
			<meta name="viewport" content="width=device-width, initial-scale=1.0"/>
			<title>Cancelled</title>
			<link
				rel="stylesheet"
				type="text/css"
				href={ models.STYLE }
			/>
			<link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>🙅</text></svg>"/>
		</head>
		<body>
			<script type="module" src={ models.JS }></script>
			<div class="flex min-h-screen items-center justify-center bg-gray-900 bg-opacity-80">
				<div class="card glass w-96 pt-6">
					<div class="animate-bounce text-center text-3xl animate-infinite animate-ease-linear">
						👎
					</div>
					<div class="card-body grid place-items-center">
						<h2 class="card-title pb-3">Payment Cancelled</h2>
						// <div class="card-actions justify-center">
						// 	<button onclick="window.location='https://example.com';" class="btn btn-primary">New Payment</button>
						// </div>
					</div>
				</div>
			</div>
		</body>
	</html>
}

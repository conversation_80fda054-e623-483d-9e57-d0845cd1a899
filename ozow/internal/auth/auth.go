package auth

import (
	aps_http "aps/lib/generic/http"
	aps_vault "aps/lib/generic/vault"
	"aps/services/ozow/internal/models"
	"bytes"
	"encoding/json"
	"log"
	"net/http"
	"net/url"
	"os"
)

const pword = "Password"

func GetToken() ([]byte, error) {
	sitecode := os.Getenv("SITE_CODE")
	baseURL := "https://stagingapi.ozow.com/token"
	PATH := os.Getenv("VAULT_PATH")
	VURL := os.Getenv("VAULT_URL")
	TOKEN := os.Getenv("VAULT_TOKEN")
	credbdy, err := aps_vault.GetSecret(PATH, VURL, TOKEN)
	if err != nil {
		return nil, err
	}
	var vcred models.MyVaultCred
	err = json.Unmarshal(credbdy, &vcred)
	if err != nil {
		return nil, err
	}

	data := url.Values{}
	data.Set("grant_type", pword)
	data.Set("SiteCode", sitecode)
	payload := bytes.NewBufferString(data.Encode())

	req, err := http.NewRequest(http.MethodPost, baseURL, payload)
	if err != nil {
		return nil, err
	}
	log.Println(vcred.Data.Apikey)
	req.Header.Set(aps_http.HeaderAccept, "application/json, application/xml")
	req.Header.Set("ApiKey", vcred.Data.Apikey)
	req.Header.Set(aps_http.HeaderContentType, aps_http.MIMEApplicationXWWWFormURLEncoded)

	bdy, err := aps_http.RegRequest(req)
	log.Println("MyRes: ", string(bdy))

	if err != nil {
		return nil, err
	}

	return bdy, nil
}

package templates

import "loginhttp/internal/models"

templ Dashboard(user *models.User) {
	@Layout("Dashboard", DashboardContent(user))
}

templ DashboardContent(user *models.User) {
	<div id="dashboard-content" class="space-y-6">
		<h2 class="text-2xl font-bold text-center text-gray-800">Welcome, { user.FullName }!</h2>
		<div class="bg-gray-50 p-4 rounded-md shadow-sm">
			<p class="text-gray-700"><span class="font-semibold">Username:</span> { user.Username }</p>
			<p class="text-gray-700"><span class="font-semibold">Email:</span> { user.Email }</p>
		</div>
		<div class="flex flex-col space-y-3">
			<button
				hx-get="/edit"
				hx-target="#main-content"
				hx-swap="outerHTML"
				class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
			>
				Edit Profile
			</button>
			<button
				hx-post="/logout"
				hx-target="body"
				hx-swap="outerHTML"
				class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
			>
				Logout
			</button>
		</div>
	</div>
}

package templates

import "loginhttp/internal/models"

templ EditProfile(user *models.User, message string) {
	@Layout("Edit Profile", EditProfileForm(user, message))
}

templ EditProfileForm(user *models.User, message string) {
	<div id="edit-form-container" class="space-y-6">
		<h2 class="text-2xl font-bold text-center text-gray-800">Edit Profile</h2>
		if message != "" {
			<div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-md relative" role="alert">
				<strong class="font-bold">Success!</strong>
				<span class="block sm:inline">{ message }</span>
			</div>
		}
		<form hx-post="/edit" hx-target="#edit-form-container" hx-swap="outerHTML" class="space-y-4">
			<div>
				<label for="fullName" class="block text-sm font-medium text-gray-700">Full Name</label>
				<input
					type="text"
					id="fullName"
					name="fullName"
					value={ user.FullName }
					required
					class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
				/>
			</div>
			<div>
				<label for="email" class="block text-sm font-medium text-gray-700">Email</label>
				<input
					type="email"
					id="email"
					name="email"
					value={ user.Email }
					required
					class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
				/>
			</div>
			<div class="flex space-x-4">
				<button
					type="submit"
					class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
				>
					Save Changes
				</button>
				<button
					type="button"
					hx-get="/dashboard"
					hx-target="#main-content"
					hx-swap="outerHTML"
					class="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
				>
					Cancel
				</button>
			</div>
		</form>
	</div>
}

package templates

templ Layout(title string, content templ.Component) {
	<!DOCTYPE html>
	<html lang="en">
		<head>
			<meta charset="UTF-8"/>
			<meta name="viewport" content="width=device-width, initial-scale=1.0"/>
			<title>{ title }</title>
			<script src="https://cdn.tailwindcss.com"></script>
			<link rel="preconnect" href="https://fonts.googleapis.com"/>
			<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin/>
			<link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet"/>
			<style>
				body {
					font-family: 'Inter', sans-serif;
				}
			</style>
			<script src="https://unpkg.com/htmx.org@1.9.10"></script>
		</head>
		<body class="bg-gray-100 flex items-center justify-center min-h-screen">
			<div id="main-content" class="w-full max-w-md bg-white p-8 rounded-lg shadow-md">
				@content
			</div>
		</body>
	</html>
}

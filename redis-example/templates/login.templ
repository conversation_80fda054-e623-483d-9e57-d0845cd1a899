package templates

type LoginData struct {
	Error string
}

templ Login(data LoginData) {
	@Layout("Login", LoginForm(data))
}

templ LoginForm(data LoginData) {
	<div id="login-form-container" class="space-y-6">
		<h2 class="text-2xl font-bold text-center text-gray-800">Login</h2>
		if data.Error != "" {
			<div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-md relative" role="alert">
				<strong class="font-bold">Error!</strong>
				<span class="block sm:inline">{ data.Error }</span>
			</div>
		}
		<form hx-post="/login" hx-target="#login-form-container" hx-swap="outerHTML" class="space-y-4">
			<div>
				<label for="username" class="block text-sm font-medium text-gray-700">Username</label>
				<input
					type="text"
					id="username"
					name="username"
					required
					class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
				/>
			</div>
			<div>
				<label for="password" class="block text-sm font-medium text-gray-700">Password</label>
				<input
					type="password"
					id="password"
					name="password"
					required
					class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
				/>
			</div>
			<button
				type="submit"
				class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
			>
				Sign In
			</button>
		</form>
	</div>
}

package models

import (
	"log"

	"golang.org/x/crypto/bcrypt"
)

type User struct {
	ID       string
	Username string
	Password string
	Email    string
	FullName string
}

var Users = map[string]*User{
	"user1": {
		ID:       "user1",
		Username: "user1",
		Password: hashPassword("password123"),
		Email:    "<EMAIL>",
		FullName: "Alice Smith",
	},
	"user2": {
		ID:       "user2",
		Username: "user2",
		Password: hashPassword("password456"),
		Email:    "<EMAIL>",
		FullName: "<PERSON> Johnson",
	},
}

func hashPassword(password string) string {
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		log.Fatalf("Error hashing password: %v", err)
	}
	return string(hashedPassword)
}

func CheckPasswordHash(password, hash string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(hash), []byte(password))
	return err == nil
}

func GetUserByID(userID string) (*User, bool) {
	user, ok := Users[userID]
	return user, ok
}

func GetUserByUsername(username string) (*User, bool) {
	user, ok := Users[username]
	return user, ok
}

func UpdateUser(user *User) {
	Users[user.ID] = user
}

package session

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"time"

	"github.com/go-redis/redis/v8"
)

type Store interface {
	SetSession(w http.ResponseWriter, userID string) error
	GetSession(r *http.Request) (string, error)
	DeleteSession(w http.ResponseWriter, r *http.Request) error
}

type RedisStore struct {
	client *redis.Client
}

func NewRedisStore(client *redis.Client) *RedisStore {
	return &RedisStore{client: client}
}

const sessionCookieName = "session_id"
const sessionDuration = 24 * 30 * time.Hour

func generateSessionID() string {
	return fmt.Sprintf("%d-%d", time.Now().UnixNano(), time.Now().UnixMicro())
}

func (s *RedisStore) SetSession(w http.ResponseWriter, userID string) error {
	sessionID := generateSessionID()
	ctx := context.Background()

	err := s.client.Set(ctx, sessionID, userID, sessionDuration).Err()
	if err != nil {
		log.Printf("Redis error setting session: %v", err)
		return fmt.Errorf("failed to set session in Redis: %w", err)
	}

	http.SetCookie(w, &http.Cookie{
		Name:     sessionCookieName,
		Value:    sessionID,
		Path:     "/",
		Expires:  time.Now().Add(sessionDuration),
		HttpOnly: true,
		Secure:   false,
		SameSite: http.SameSiteLaxMode,
	})
	return nil
}

func (s *RedisStore) GetSession(r *http.Request) (string, error) {
	cookie, err := r.Cookie(sessionCookieName)
	if err != nil {
		if err == http.ErrNoCookie {
			return "", nil
		}
		return "", fmt.Errorf("error getting session cookie: %w", err)
	}

	sessionID := cookie.Value
	ctx := context.Background()

	userID, err := s.client.Get(ctx, sessionID).Result()
	if err != nil {
		if err == redis.Nil {

			return "", nil
		}
		log.Printf("Redis error getting session: %v", err)
		return "", fmt.Errorf("failed to get session from Redis: %w", err)
	}

	return userID, nil
}

func (s *RedisStore) DeleteSession(w http.ResponseWriter, r *http.Request) error {
	cookie, err := r.Cookie(sessionCookieName)
	if err == nil {
		ctx := context.Background()
		// Delete session from Redis
		s.client.Del(ctx, cookie.Value)

		// Expire the cookie in the browser
		http.SetCookie(w, &http.Cookie{
			Name:     sessionCookieName,
			Value:    "",
			Path:     "/",
			Expires:  time.Unix(0, 0), // Set to a past time to expire
			HttpOnly: true,
			Secure:   false, // Set to true in production with HTTPS
			SameSite: http.SameSiteLaxMode,
		})
	}
	return nil
}

package handlers

import (
	"log"
	"net/http"

	"loginhttp/internal/models"
	"loginhttp/internal/session"
	"loginhttp/templates"
)

type Handlers struct {
	SessionStore session.Store
}

func NewHandlers(sessionStore session.Store) *Handlers {
	return &Handlers{SessionStore: sessionStore}
}

func (h *Handlers) HomeHandler(w http.ResponseWriter, r *http.Request) {
	userID, err := h.SessionStore.GetSession(r)
	if err != nil {
		log.Printf("Error getting session in HomeHandler: %v", err)
		http.Error(w, "Internal server error", http.StatusInternalServerError)
		return
	}

	if userID != "" {
		http.Redirect(w, r, "/dashboard", http.StatusFound)
		return
	}
	http.Redirect(w, r, "/login", http.StatusFound)
}

func (h *Handlers) LoginGetHandler(w http.ResponseWriter, r *http.Request) {
	templates.Login(templates.LoginData{Error: ""}).Render(r.Context(), w)
}

// LoginPostHandler handles login form submission
func (h *Handlers) LoginPostHandler(w http.ResponseWriter, r *http.Request) {
	username := r.FormValue("username")
	password := r.FormValue("password")

	user, ok := models.GetUserByUsername(username)
	if !ok || !models.CheckPasswordHash(password, user.Password) {
		// If HTMX request, swap only the form with error
		if r.Header.Get("HX-Request") == "true" {
			templates.LoginForm(templates.LoginData{Error: "Invalid username or password"}).Render(r.Context(), w)
			return
		}
		// Otherwise, render full login page with error
		templates.Login(templates.LoginData{Error: "Invalid username or password"}).Render(r.Context(), w)
		return
	}

	if err := h.SessionStore.SetSession(w, user.ID); err != nil {
		log.Printf("Error setting session: %v", err)
		http.Error(w, "Internal server error", http.StatusInternalServerError)
		return
	}

	if r.Header.Get("HX-Request") == "true" {
		w.Header().Set("HX-Redirect", "/dashboard")
		w.WriteHeader(http.StatusOK)
		return
	}

	http.Redirect(w, r, "/dashboard", http.StatusFound)
}

func (h *Handlers) DashboardGetHandler(w http.ResponseWriter, r *http.Request) {
	userID, ok := GetUserIDFromContext(r)
	if !ok {

		h.SessionStore.DeleteSession(w, r)
		http.Redirect(w, r, "/login", http.StatusSeeOther)
		return
	}

	user, ok := models.GetUserByID(userID)
	if !ok {

		h.SessionStore.DeleteSession(w, r)
		http.Redirect(w, r, "/login", http.StatusSeeOther)
		return
	}

	if r.Header.Get("HX-Request") == "true" {
		templates.DashboardContent(user).Render(r.Context(), w)
		return
	}

	templates.Dashboard(user).Render(r.Context(), w)
}

func (h *Handlers) EditProfileGetHandler(w http.ResponseWriter, r *http.Request) {
	userID, ok := GetUserIDFromContext(r)
	if !ok {
		h.SessionStore.DeleteSession(w, r)
		http.Redirect(w, r, "/login", http.StatusSeeOther)
		return
	}

	user, ok := models.GetUserByID(userID)
	if !ok {
		h.SessionStore.DeleteSession(w, r)
		http.Redirect(w, r, "/login", http.StatusSeeOther)
		return
	}

	if r.Header.Get("HX-Request") == "true" {
		templates.EditProfileForm(user, "").Render(r.Context(), w)
		return
	}

	templates.EditProfile(user, "").Render(r.Context(), w)
}

func (h *Handlers) EditProfilePostHandler(w http.ResponseWriter, r *http.Request) {
	userID, ok := GetUserIDFromContext(r)
	if !ok {
		h.SessionStore.DeleteSession(w, r)
		http.Redirect(w, r, "/login", http.StatusSeeOther)
		return
	}

	user, ok := models.GetUserByID(userID)
	if !ok {
		h.SessionStore.DeleteSession(w, r)
		http.Redirect(w, r, "/login", http.StatusSeeOther)
		return
	}

	user.FullName = r.FormValue("fullName")
	user.Email = r.FormValue("email")

	models.UpdateUser(user)

	if r.Header.Get("HX-Request") == "true" {
		templates.EditProfileForm(user, "Profile updated successfully!").Render(r.Context(), w)
		return
	}

	http.Redirect(w, r, "/dashboard", http.StatusFound)
}

func (h *Handlers) LogoutPostHandler(w http.ResponseWriter, r *http.Request) {
	if err := h.SessionStore.DeleteSession(w, r); err != nil {
		log.Printf("Error deleting session on logout: %v", err)
		http.Error(w, "Internal server error", http.StatusInternalServerError)
		return
	}

	if r.Header.Get("HX-Request") == "true" {
		w.Header().Set("HX-Redirect", "/login")
		w.WriteHeader(http.StatusOK)
		return
	}

	http.Redirect(w, r, "/login", http.StatusFound)
}

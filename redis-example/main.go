package main

import (
	"fmt"
	"log"
	"net/http"

	"loginhttp/internal/handlers"
	"loginhttp/internal/session"

	"github.com/go-redis/redis/v8"
)

func main() {
	rdb := redis.NewClient(&redis.Options{
		Addr:     "<env>",
		Password: "<env>",
		DB:       1,
	})

	ctx := rdb.Context()
	_, err := rdb.Ping(ctx).Result()
	if err != nil {
		log.Fatalf("Could not connect to Redis: %v", err)
	}
	fmt.Println("Successfully connected to Redis!")

	sessionStore := session.NewRedisStore(rdb)

	appHandlers := handlers.NewHandlers(sessionStore)

	http.HandleFunc("/", appHandlers.HomeHandler)
	http.HandleFunc("/login", func(w http.ResponseWriter, r *http.Request) {
		if r.Method == http.MethodGet {
			appHandlers.LoginGetHandler(w, r)
		} else if r.Method == http.MethodPost {
			appHandlers.LoginPostHandler(w, r)
		} else {
			http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		}
	})
	http.HandleFunc("/logout", func(w http.ResponseWriter, r *http.Request) {
		if r.Method == http.MethodPost {
			appHandlers.LogoutPostHandler(w, r)
		} else {
			http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		}
	})

	http.Handle("/dashboard", handlers.AuthMiddleware(sessionStore, http.HandlerFunc(appHandlers.DashboardGetHandler)))
	http.Handle("/edit", handlers.AuthMiddleware(sessionStore, http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.Method {
		case http.MethodGet:
			appHandlers.EditProfileGetHandler(w, r)
		case http.MethodPost:
			appHandlers.EditProfilePostHandler(w, r)
		default:
			http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		}
	})))

	port := ":8080"
	fmt.Printf("Server starting on port %s\n", port)
	log.Fatal(http.ListenAndServe(port, nil)) // Use nil
}
